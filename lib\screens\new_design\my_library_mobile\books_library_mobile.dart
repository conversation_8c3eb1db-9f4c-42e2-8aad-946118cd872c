import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/screens/new_design/my_library_mobile/solutions_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/objects_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/add_modules_mobileview.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/widgets/mobile/mobile_nav_item.dart';

class BookMobile {
  final String title;
  final String subtitle;
  final String imageUrl;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;

  BookMobile({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    this.isDraft = false,
    this.imageWidth = 101.0,
    this.imageHeight = 156.0,
  });

  factory BookMobile.fromJson(Map<String, dynamic> json) {
    return BookMobile(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      imageUrl: json['imageUrl'] as String,
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 101.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 156.0,
    );
  }
}

class BooksLibraryMobile extends StatefulWidget {
  const BooksLibraryMobile({
    super.key,
    this.showNavigationBar = true,
  });

  final bool showNavigationBar;

  @override
  State<BooksLibraryMobile> createState() => _BooksLibraryMobileState();
}

class _BooksLibraryMobileState extends State<BooksLibraryMobile>
    with TickerProviderStateMixin {
  // Data
  late List<BookMobile> books;
  bool isLoading = true;

  // Navigation
  int selectedTabIndex = 0;

  // Carousel state
  int _currentIndex = 0;
  double _carouselPosition = 0.0;

  // Configuration constants
  static const double _booksPerView = 2.75;
  static const double _compactBooksPerView = 3.0;

  // Book aspect ratio and spacing constants
  static const double _bookAspectRatio = 101.0 / 156.0; // Width to height ratio
  static const double _titleHeight = 32.0; // 2 lines * 16px line height
  static const double _subtitleHeight = 16.0; // 1 line * 16px line height
  static const double _verticalSpacing = 12.0; // 8px + 4px between elements

  // Controllers
  late CarouselController _carouselController;
  final FocusNode _searchFocusNode = FocusNode();
  late AnimationController _loadingAnimationController;

  // Animations
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // UI state
  bool _isKeyboardVisible = false;

  // JSON data
  static const String booksJsonString = '''
{
  "books": [
    {
      "title": "Ecommerce Platform Solutions",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book_01.png",
      "isDraft": false
    },
    {
      "title": "Fashion & Apparel Store",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-02.png",
      "isDraft": false
    },
    {
      "title": "Financial Advisory Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-03.png",
      "isDraft": false
    },
    {
      "title": "Home Rentals App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": true
    },
    {
      "title": "Online Grocery Store",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-05.png",
      "isDraft": false
    },
    {
      "title": "Courier & Logistics",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-06.png",
      "isDraft": false
    },
    {
      "title": "Automotive Marketplace",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-07.png",
      "isDraft": true
    },
    {
      "title": "Fitness & Wellness App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-08.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-09.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Healthcare Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Education Portal",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Travel Booking App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Music Streaming",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Social Media Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Gaming Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "News & Media App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Banking App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Investment Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Delivery Service",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Job Portal",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Event Management",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Video Streaming",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Smart Home IoT",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Cryptocurrency Exchange",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadBooks();
  }

  void _initializeControllers() {
    _carouselController = CarouselController();
    _searchFocusNode.addListener(_onSearchFocusChange);
  }

  void _initializeAnimations() {
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _onSearchFocusChange() {
    setState(() {
      // Trigger rebuild to check keyboard visibility in build method
    });
  }

  void _loadBooks() {
    try {
      final data = json.decode(booksJsonString);
      final loadedBooks = (data['books'] as List<dynamic>)
          .map((bookJson) =>
              BookMobile.fromJson(bookJson as Map<String, dynamic>))
          .toList();

      setState(() {
        books = loadedBooks;
        isLoading = false;
      });

      _loadingAnimationController.forward();
    } catch (e) {
      setState(() {
        books = <BookMobile>[];
        isLoading = false;
      });
      debugPrint('Error loading books: $e');
    }
  }

  @override
  void dispose() {
    _carouselController.dispose();
    _searchFocusNode.removeListener(_onSearchFocusChange);
    _searchFocusNode.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _updateKeyboardVisibility();
    return _buildBooksLibraryView();
  }

  void _updateKeyboardVisibility() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    if (_isKeyboardVisible != isKeyboardVisible) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isKeyboardVisible = isKeyboardVisible;
            // Maintain current position when keyboard visibility changes
            _adjustCarouselForKeyboard();
          });
        }
      });
    }
  }

  /// Adjusts carousel position when keyboard visibility changes
  void _adjustCarouselForKeyboard() {
    // Store current position
    final currentPageIndex = _getCurrentPageIndex();

    // Calculate target index for the new layout
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final targetIndex = (currentPageIndex * _getBooksPerView()).round();
        final clampedIndex = targetIndex.clamp(0, books.length - 1);

        if (_currentIndex != clampedIndex) {
          _animateCarouselToIndex(clampedIndex);
        }
      }
    });
  }

  Widget _buildBooksLibraryView() {
    return Scaffold(
      backgroundColor: widget.showNavigationBar
          ? const Color(0xfff6f6f6)
          : Colors.transparent,
      drawer: widget.showNavigationBar ? const CustomDrawer() : null,
      appBar: widget.showNavigationBar ? _buildAppBar() : null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showNavigationBar) _buildTopNavigation(),
          if (widget.showNavigationBar) _buildSearchAndCreateSection(),
          _buildCustomSwiper(),
        ],
      ),
      floatingActionButton:
          widget.showNavigationBar ? _buildFloatingActionButton() : null,
    );
  }

  Widget _buildCustomSwiper() {
    return Expanded(
      child: NSLKnowledgeLoaderWrapper(
        isLoading: isLoading,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 0, 8),
          child: Column(
            children: [
              // CarouselView with individual books
              SizedBox(
                height: _calculateCarouselHeight(),
                child: _buildControllerLinkedCarousel(),
              ),
              // Custom Dot Pagination (show dots when needed)
              if (_calculatePaginationCount() > 1)
                _buildCustomPagination(_calculatePaginationCount()),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds carousel with direct controller integration for pagination
  Widget _buildControllerLinkedCarousel() {
    // Handle empty books case
    if (books.isEmpty) {
      return const Center(
        child: Text(
          'No books found',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
            fontFamily: "TiemposText",
          ),
        ),
      );
    }

    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        if (notification is ScrollUpdateNotification ||
            notification is ScrollEndNotification) {
          _updateCarouselPosition(notification);
        }
        return false;
      },
      child: CarouselView(
        padding: EdgeInsets.zero,
        backgroundColor: Colors.transparent,
        controller: _carouselController,
        itemExtent: _calculateItemExtent(),
        enableSplash: false,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        shrinkExtent: _calculateItemExtent(),
        children: books.asMap().entries.map((entry) {
          final bookIndex = entry.key;
          final book = entry.value;
          return _buildBookItem(book, bookIndex);
        }).toList(),
      ),
    );
  }

  /// Calculate item extent for CarouselView
  double _calculateItemExtent() {
    final bookDimensions = _calculateBookDimensions(context);
    return bookDimensions['width']!;
  }

  /// Updates carousel position and pagination based on scroll notification
  void _updateCarouselPosition(ScrollNotification notification) {
    final scrollOffset = notification.metrics.pixels;
    final itemExtent = _calculateItemExtent();

    if (itemExtent > 0 && books.isNotEmpty) {
      final rawIndex = scrollOffset / itemExtent;
      final newIndex = rawIndex.round();
      final clampedIndex = newIndex.clamp(0, books.length - 1);

      if (_currentIndex != clampedIndex) {
        setState(() {
          _currentIndex = clampedIndex;
          _carouselPosition = rawIndex;
        });
      }
    }
  }

  /// Builds individual book item widget
  Widget _buildBookItem(BookMobile book, int bookIndex) {
    return GestureDetector(
      onTap: () => _navigateToBookDetails(bookIndex),
      child: AnimatedBuilder(
        animation: _loadingAnimationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBookContent(book),
            ),
          );
        },
      ),
    );
  }

  /// Builds the book content (cover, title, subtitle)
  Widget _buildBookContent(BookMobile book) {
    final bookDimensions = _calculateBookDimensions(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildBookCover(book, bookDimensions),
        const SizedBox(height: 8),
        _buildBookTitle(book.title, bookDimensions['width']!),
        const SizedBox(height: 4),
        _buildBookSubtitle(book.subtitle, bookDimensions['width']!),
      ],
    );
  }

  /// Builds book cover with SVG background and draft badge
  Widget _buildBookCover(BookMobile book, Map<String, double> dimensions) {
    final bookWidth = dimensions['width']!;
    final bookHeight = dimensions['height']!;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Background SVG shape
        Positioned(
          right: -8,
          bottom: 0,
          child: SvgPicture.asset(
            'assets/images/home-lib-shape.svg',
            width: bookWidth * 0.925,
            height: bookHeight * 0.95,
            fit: BoxFit.contain,
          ),
        ),
        // Main book image
        AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: bookWidth,
          height: bookHeight,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            image: DecorationImage(
              image: AssetImage(book.imageUrl),
              fit: BoxFit.cover,
            ),
          ),
        ),
        // Draft badge
        if (book.isDraft) _buildDraftBadge(bookWidth, bookHeight),
      ],
    );
  }

  /// Builds draft badge for books
  Widget _buildDraftBadge(double bookWidth, double bookHeight) {
    return Positioned(
      top: bookHeight * 0.08,
      right: bookWidth * 0.14,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.amber,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Colors.amber.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Text(
          'Draft',
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: Colors.black,
            fontFamily: "TiemposText",
          ),
        ),
      ),
    );
  }

  /// Builds book title
  Widget _buildBookTitle(String title, double bookWidth) {
    return SizedBox(
      width: bookWidth,
      child: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 12,
          height: 1.334,
          color: Colors.black,
          fontFamily: "TiemposText",
        ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Builds book subtitle
  Widget _buildBookSubtitle(String subtitle, double bookWidth) {
    return SizedBox(
      width: bookWidth,
      child: Text(
        subtitle,
        style: const TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 11,
          color: Colors.black,
          fontFamily: "TiemposText",
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Navigates to book details screen
  void _navigateToBookDetails(int bookIndex) {
    setState(() {
      _currentIndex = bookIndex;
    });
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddModulesMobileView(),
      ),
    );
  }

  /// Calculate pagination count with UX-friendly limits
  int _calculatePaginationCount() {
    if (books.isEmpty) return 0;

    final booksPerView = _getBooksPerView();

    // If we have very few books that fit comfortably in one view, no pagination needed
    if (books.length <= booksPerView.floor()) {
      debugPrint(
          'Pagination calculation: books=${books.length}, booksPerView=$booksPerView, result=1 (all books fit)');
      return 1;
    }

    // Calculate total possible scroll positions
    final maxStartingPosition = books.length - booksPerView.floor();
    final totalScrollPositions = maxStartingPosition.ceil();

    // Limit pagination dots to maximum 5 for better UX
    const maxPaginationDots = 5;
    final paginationCount = totalScrollPositions.clamp(1, maxPaginationDots);

    debugPrint(
        'Pagination calculation: books=${books.length}, booksPerView=$booksPerView, totalScrollPositions=$totalScrollPositions, limited to=$paginationCount');

    return paginationCount;
  }

  /// Get current page index for pagination dots with smart mapping
  int _getCurrentPageIndex() {
    if (books.isEmpty) return 0;

    final booksPerView = _getBooksPerView();

    // If all books fit in one view, always return page 0
    if (books.length <= booksPerView.floor()) {
      return 0;
    }

    final paginationCount = _calculatePaginationCount();
    final maxBookIndex = books.length - 1;

    // Map current book index to pagination index
    // This creates even distribution across available pagination dots
    final normalizedPosition = _currentIndex / maxBookIndex;
    final pageIndex = (normalizedPosition * (paginationCount - 1)).round();

    debugPrint(
        'Current page calculation: _currentIndex=$_currentIndex, normalizedPosition=$normalizedPosition, pageIndex=$pageIndex, totalPages=$paginationCount');

    return pageIndex.clamp(0, paginationCount - 1);
  }

  /// Get books per view based on keyboard visibility
  double _getBooksPerView() {
    return _isKeyboardVisible ? _compactBooksPerView : _booksPerView;
  }

  /// Calculate dynamic book dimensions based on available space
  Map<String, double> _calculateBookDimensions(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final booksPerView = _getBooksPerView();

    // Calculate available width (accounting for padding)
    const double leftPadding = 16.0;
    const double rightPadding = 0.0; // Right padding handled by carousel
    final double availableWidth = screenWidth - leftPadding - rightPadding;

    // Calculate book width based on books per view
    double bookWidth = availableWidth / booksPerView;

    // Apply reasonable constraints
    if (_isKeyboardVisible) {
      // Compact mode: smaller books
      bookWidth = bookWidth.clamp(70.0, 110.0);
    } else {
      // Normal mode: larger books
      bookWidth = bookWidth.clamp(85.0, 130.0);
    }

    // Calculate height maintaining aspect ratio
    double bookHeight = bookWidth / _bookAspectRatio;

    return {
      'width': bookWidth,
      'height': bookHeight,
    };
  }

  /// Calculate dynamic height for CarouselView based on book content
  double _calculateCarouselHeight() {
    final bookDimensions = _calculateBookDimensions(context);
    final bookHeight = bookDimensions['height']!;

    // Base height includes: book image + spacing + title + spacing + subtitle
    return bookHeight + _verticalSpacing + _titleHeight + _subtitleHeight + 6;
  }

  /// Builds custom pagination dots with overflow protection
  Widget _buildCustomPagination(int pageCount) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Center(
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(
              pageCount,
              (index) => _buildPaginationDot(index),
            ),
          ),
        ),
      ),
    );
  }

  /// Navigate to specific page programmatically with smart mapping
  void navigateToPage(int pageIndex) {
    if (pageIndex < 0 ||
        pageIndex >= _calculatePaginationCount() ||
        books.isEmpty) {
      return;
    }

    final booksPerView = _getBooksPerView();

    int targetBookIndex;

    if (books.length <= booksPerView.floor()) {
      // All books fit in one view
      targetBookIndex = 0;
    } else {
      final paginationCount = _calculatePaginationCount();
      final maxBookIndex = books.length - 1;

      // Map pagination index to book index
      final normalizedPosition = pageIndex / (paginationCount - 1);
      targetBookIndex = (normalizedPosition * maxBookIndex).round();
    }

    final clampedIndex = targetBookIndex.clamp(0, books.length - 1);

    _animateCarouselToIndex(clampedIndex);
  }

  /// Get current carousel position as percentage (0.0 to 1.0)
  double get carouselProgress {
    if (books.isEmpty) return 0.0;
    return _currentIndex / (books.length - 1);
  }

  /// Builds individual pagination dot with compact design
  Widget _buildPaginationDot(int index) {
    final currentPageIndex = _getCurrentPageIndex();
    final isActive = currentPageIndex == index;

    debugPrint(
        'Pagination dot $index: currentPageIndex=$currentPageIndex, isActive=$isActive, _currentIndex=$_currentIndex, booksPerView=${_getBooksPerView()}');

    return GestureDetector(
      onTap: () => _onPaginationDotTap(index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOutCubic,
        margin: const EdgeInsets.symmetric(horizontal: 3), // Reduced margin
        width: isActive ? 20 : 6, // Reduced sizes
        height: 6, // Reduced height
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(3),
          color: isActive ? const Color(0xff0058FF) : Colors.grey.shade300,
          boxShadow: isActive
              ? [
                  BoxShadow(
                    color: const Color(0xff0058FF).withValues(alpha: 0.3),
                    blurRadius: 4, // Reduced blur
                    offset: const Offset(0, 1), // Reduced offset
                    spreadRadius: 0.5, // Reduced spread
                  ),
                ]
              : null, // Removed inactive shadow to save space
        ),
        child: isActive
            ? Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(3),
                  gradient: const LinearGradient(
                    colors: [Color(0xff0058FF), Color(0xff0040CC)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              )
            : null,
      ),
    );
  }

  /// Handles pagination dot tap with smart book index mapping
  void _onPaginationDotTap(int pageIndex) {
    if (books.isEmpty) return;

    final booksPerView = _getBooksPerView();

    int targetBookIndex;

    if (books.length <= booksPerView.floor()) {
      // All books fit in one view
      targetBookIndex = 0;
    } else {
      final paginationCount = _calculatePaginationCount();
      final maxBookIndex = books.length - 1;

      // Map pagination index back to book index
      // This distributes the book positions evenly across pagination dots
      final normalizedPosition = pageIndex / (paginationCount - 1);
      targetBookIndex = (normalizedPosition * maxBookIndex).round();
    }

    // Ensure target index is within bounds
    final clampedIndex = targetBookIndex.clamp(0, books.length - 1);

    // Debug information
    debugPrint(
        'Pagination dot tapped: pageIndex=$pageIndex, targetBookIndex=$targetBookIndex, clampedIndex=$clampedIndex');

    // Animate to target position using CarouselController
    _animateCarouselToIndex(clampedIndex);
  }

  /// Animates carousel to specific index using CarouselController
  void _animateCarouselToIndex(int targetIndex) {
    if (targetIndex < 0 || targetIndex >= books.length) {
      debugPrint(
          'Invalid target index: $targetIndex (books.length: ${books.length})');
      return;
    }

    // Calculate the scroll position in pixels based on item extent
    final itemExtent = _calculateItemExtent();
    final targetScrollPosition = targetIndex * itemExtent;

    debugPrint(
        'Animating carousel: targetIndex=$targetIndex, itemExtent=$itemExtent, targetScrollPosition=$targetScrollPosition');

    _carouselController.animateTo(
      targetScrollPosition,
      duration: const Duration(milliseconds: 350),
      curve: Curves.easeInOutCubic,
    );

    // Update current index immediately for responsive UI
    setState(() {
      _currentIndex = targetIndex;
      _carouselPosition = targetIndex.toDouble();
    });
  }

  /// Builds floating action button
  Widget _buildFloatingActionButton() {
    return SizedBox(
      width: 46,
      height: 46,
      child: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateBookMobile(),
            ),
          );
        },
        backgroundColor: const Color(0xff0058FF),
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Builds app bar for the screen
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xfff6f6f6),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          Expanded(
            child: Text(
              AppLocalizations.of(context).translate('library.pageTitle'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 56),
        ],
      ),
    );
  }

  /// Builds top navigation tabs
  Widget _buildTopNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          MobileNavItem(
            iconPath: 'assets/images/books-icon.svg',
            label: AppLocalizations.of(context).translate('library.books'),
            isActive: selectedTabIndex == 0,
            onTap: () {},
          ),
          MobileNavItem(
            iconPath: 'assets/images/square-box-uncheck.svg',
            label: AppLocalizations.of(context).translate('library.solutions'),
            isActive: selectedTabIndex == 1,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SolutionsLibraryMobile(),
                ),
              );
            },
          ),
          MobileNavItem(
            iconPath: 'assets/images/cube-box.svg',
            label: AppLocalizations.of(context).translate('library.objects'),
            isActive: selectedTabIndex == 2,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ObjectsLibraryMobile(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// Builds search and filter section
  Widget _buildSearchAndCreateSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16.0),
                    child: TextField(
                      focusNode: _searchFocusNode,
                      decoration: InputDecoration(
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        hintText: 'Search',
                        border: InputBorder.none,
                        hintStyle: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[500],
                        ),
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                ),
                _MobileSvgButton(
                  iconPath: 'assets/images/search.svg',
                  onPressed: () {},
                  size: 20,
                ),
                Container(
                  height: 24,
                  width: 1,
                  color: Colors.grey.shade200,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                ),
                _MobileSvgButton(
                  iconPath: 'assets/images/filter-icon.svg',
                  onPressed: () {},
                  size: 24,
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          // const SizedBox(height: 12),
        ],
      ),
    );
  }
}

// Mobile SVG Button Widget
class _MobileSvgButton extends StatefulWidget {
  final String iconPath;
  final VoidCallback onPressed;
  final double size;

  const _MobileSvgButton({
    required this.iconPath,
    required this.onPressed,
    this.size = 18,
  });

  @override
  State<_MobileSvgButton> createState() => _MobileSvgButtonState();
}

class _MobileSvgButtonState extends State<_MobileSvgButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: SvgPicture.asset(
            widget.iconPath,
            width: widget.size,
            height: widget.size,
          ),
        ),
      ),
    );
  }
}
